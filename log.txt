{"timestamp": "2025-02-08 17:47:26", "level": "info", "message": "获取 Token 中..."}
{"timestamp": "2025-02-08 17:47:26", "level": "success", "message": "Token 获取成功"}
{"timestamp": "2025-02-08 17:47:53", "level": "info", "message": "选择的文件: C:/Users/<USER>/Desktop/新建 XLS 工作表 (3).xls"}
{"timestamp": "2025-02-08 17:47:54", "level": "info", "message": "设备报备: 1"}
{"timestamp": "2025-02-08 17:47:54", "level": "success", "message": "设备报备成功: 1"}
{"timestamp": "2025-02-08 17:47:54", "level": "info", "message": "设备报备: 2"}
{"timestamp": "2025-02-08 17:47:54", "level": "success", "message": "设备报备成功: 2"}
{"timestamp": "2025-02-08 17:47:54", "level": "info", "message": "设备报备: 3"}
{"timestamp": "2025-02-08 17:47:54", "level": "success", "message": "设备报备成功: 3"}
{"timestamp": "2025-02-08 17:47:54", "level": "success", "message": "批量报备完成"}
