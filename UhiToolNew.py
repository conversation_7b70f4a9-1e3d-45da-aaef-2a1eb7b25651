import requests
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from datetime import datetime
import pandas as pd

# 常量配置
TOKEN_URL = "https://ezopen.uhinetworks.com/api/lapp/token/get"
DEVICE_REPORT_URL = "https://ezopen.uhinetworks.com/sap/device/license/add"
APP_KEY = "e75fd11a715b4938a65bc78f4ecf9a30"
APP_SECRET = "6e5a77756d2be738b549f4186a8f2e5d"
HEADERS = {"Content-Type": "application/x-www-form-urlencoded"}

# 日志文件路径
LOG_FILE = "log.txt"


def log_message(message, level="info"):
    log_entry = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "level": level,
        "message": message
    }
    log_text.insert(tk.END, json.dumps(log_entry, ensure_ascii=False, indent=4) + "\n")
    log_text.yview(tk.END)
    root.update_idletasks()
    with open(LOG_FILE, "a", encoding="utf-8") as log_file:
        log_file.write(json.dumps(log_entry, ensure_ascii=False) + "\n")


def get_token():
    try:
        log_message("获取 Token 中...", "info")
        response = requests.post(TOKEN_URL, data={"appKey": APP_KEY, "appSecret": APP_SECRET}, headers=HEADERS,
                                 timeout=5)
        response.raise_for_status()
        token_data = response.json()
        access_token = token_data.get("data", {}).get("accessToken")
        if access_token:
            log_message("Token 获取成功", "success")
            return access_token
    except requests.RequestException as e:
        log_message(f"获取 Token 失败: {e}", "error")
    return None


def report_device(device_serial, verification_code):
    if not token:
        messagebox.showerror("错误", "Token 获取失败，请检查网络连接")
        return

    headers = {"zuulUserId": token, "Content-Type": "application/json"}
    data = [{"deviceSerial": device_serial, "verificationCode": verification_code}]

    try:
        log_message(f"设备报备: {device_serial}", "info")
        response = requests.post(DEVICE_REPORT_URL, json=data, headers=headers, timeout=5)
        response.raise_for_status()
        log_message(f"设备报备成功: {device_serial}", "success")
    except requests.RequestException as e:
        log_message(f"设备报备失败: {e}", "error")


def choose_file():
    global file_path
    file_path = filedialog.askopenfilename(filetypes=[("Excel 文件", "*.xls;*.xlsx")])
    if file_path:
        log_message(f"选择的文件: {file_path}", "info")


def submit_batch_report():
    if not token:
        messagebox.showerror("错误", "Token 获取失败，请检查网络连接")
        return

    if not file_path:
        messagebox.showwarning("提示", "请选择文件")
        return

    try:
        df = pd.read_excel(file_path)
        if '设备序列号' not in df.columns or '验证码' not in df.columns:
            log_message("文件格式错误，缺少必要列", "error")
            return

        total_devices = len(df)
        progress_bar["value"] = 0
        progress_bar["maximum"] = total_devices

        for i, row in df.iterrows():
            device_serial = str(row['设备序列号']).strip()
            verification_code = str(row['验证码']).strip()
            if device_serial and verification_code:
                report_device(device_serial, verification_code)
            progress_bar["value"] = i + 1
            progress_label.config(text=f"{int((i + 1) / total_devices * 100)}%")
            root.update_idletasks()

        log_message("批量报备完成", "success")
    except Exception as e:
        log_message(f"批量报备失败: {e}", "error")


root = tk.Tk()
root.title("设备报备工具")

main_frame = ttk.Frame(root)
main_frame.pack(padx=20, pady=20)

log_frame = ttk.Frame(root)
log_frame.pack(padx=20, pady=20, fill="both", expand=True)

label_serial = ttk.Label(main_frame, text="设备序列号:")
label_serial.grid(row=0, column=0, padx=5, pady=5)
entry_serial = ttk.Entry(main_frame, width=40)
entry_serial.grid(row=0, column=1, padx=5, pady=5)

label_code = ttk.Label(main_frame, text="验证码:")
label_code.grid(row=1, column=0, padx=5, pady=5)
entry_code = ttk.Entry(main_frame, width=40)
entry_code.grid(row=1, column=1, padx=5, pady=5)

submit_button = ttk.Button(main_frame, text="提交", command=lambda: report_device(entry_serial.get(), entry_code.get()))
submit_button.grid(row=2, column=0, columnspan=2, pady=10)

choose_button = ttk.Button(main_frame, text="选择文件", command=choose_file)
choose_button.grid(row=3, column=0, columnspan=2, pady=10)

submit_batch_button = ttk.Button(main_frame, text="提交批量报备", command=submit_batch_report)
submit_batch_button.grid(row=4, column=0, columnspan=2, pady=10)

progress_bar = ttk.Progressbar(main_frame, length=300, mode='determinate')
progress_bar.grid(row=5, column=0, columnspan=2, pady=10)
progress_label = ttk.Label(main_frame, text="0%")
progress_label.grid(row=6, column=0, columnspan=2, pady=5)

log_label = ttk.Label(log_frame, text="日志:")
log_label.pack(anchor="w")
log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
log_text.pack(fill="both", expand=True)
log_scrollbar = ttk.Scrollbar(log_frame, command=log_text.yview)
log_scrollbar.pack(side="right", fill="y")
log_text.config(yscrollcommand=log_scrollbar.set)

token = get_token()
root.mainloop()
